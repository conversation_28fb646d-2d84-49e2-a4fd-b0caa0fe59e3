{% extends "base.html" %}

{% block title %}统计信息 - 中兴网管告警查看器{% endblock %}
{% block page_title %}统计信息{% endblock %}

{% block extra_css %}
<style>
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 20px;
        text-align: center;
    }
    
    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        color: #007bff;
        margin-bottom: 10px;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 1.1em;
    }
    
    .chart-container {
        height: 300px;
        margin: 20px 0;
    }
    
    .stats-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .stats-table th,
    .stats-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }
    
    .stats-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }
    
    .progress-bar {
        background-color: #e9ecef;
        border-radius: 4px;
        height: 20px;
        overflow: hidden;
        margin-left: 10px;
        flex: 1;
    }
    
    .progress-fill {
        height: 100%;
        background-color: #007bff;
        transition: width 0.3s ease;
    }
    
    .stats-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .stats-label {
        min-width: 120px;
        font-weight: 500;
    }
    
    .stats-count {
        min-width: 60px;
        text-align: right;
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div id="loading" style="text-align: center; padding: 50px;">
    <p>正在加载统计数据...</p>
</div>

<div id="stats-content" style="display: none;">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number" id="total-count">-</div>
            <div class="stat-label">告警总数</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number" id="critical-count" style="color: #dc3545;">-</div>
            <div class="stat-label">严重告警</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number" id="major-count" style="color: #fd7e14;">-</div>
            <div class="stat-label">主要告警</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number" id="minor-count" style="color: #ffc107;">-</div>
            <div class="stat-label">次要告警</div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            告警级别分布
        </div>
        <div class="card-body">
            <div id="severity-stats"></div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            告警类型分布 (Top 10)
        </div>
        <div class="card-body">
            <div id="type-stats"></div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            确认状态分布
        </div>
        <div class="card-body">
            <div id="ack-stats"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function loadStats() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            // 更新总数统计
            document.getElementById('total-count').textContent = data.total_count.toLocaleString();
            
            // 更新级别统计
            const severityMap = {};
            data.severity_stats.forEach(item => {
                severityMap[item.name] = item.count;
            });
            
            document.getElementById('critical-count').textContent = (severityMap['严重'] || 0).toLocaleString();
            document.getElementById('major-count').textContent = (severityMap['主要'] || 0).toLocaleString();
            document.getElementById('minor-count').textContent = (severityMap['次要'] || 0).toLocaleString();
            
            // 渲染级别分布
            renderStatsChart('severity-stats', data.severity_stats, data.total_count);
            
            // 渲染类型分布
            renderStatsChart('type-stats', data.type_stats, data.total_count);
            
            // 渲染确认状态分布
            renderStatsChart('ack-stats', data.ack_stats, data.total_count);
            
            // 显示内容，隐藏加载提示
            document.getElementById('loading').style.display = 'none';
            document.getElementById('stats-content').style.display = 'block';
        })
        .catch(error => {
            console.error('加载统计数据失败:', error);
            document.getElementById('loading').innerHTML = '<p style="color: red;">加载统计数据失败</p>';
        });
}

function renderStatsChart(containerId, data, total) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    data.forEach(item => {
        const percentage = ((item.count / total) * 100).toFixed(1);
        
        const row = document.createElement('div');
        row.className = 'stats-row';
        
        row.innerHTML = `
            <div class="stats-label">${item.name}</div>
            <div class="stats-count">${item.count.toLocaleString()}</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${percentage}%"></div>
            </div>
            <div style="margin-left: 10px; min-width: 50px; font-size: 0.9em; color: #6c757d;">
                ${percentage}%
            </div>
        `;
        
        container.appendChild(row);
    });
}

// 页面加载完成后获取统计数据
document.addEventListener('DOMContentLoaded', loadStats);
</script>
{% endblock %}
