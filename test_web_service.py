#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_web_service():
    """测试Web服务的各个接口"""

    base_url = "http://localhost:8080"
    
    print("=== 测试Web服务 ===")
    
    # 测试主页
    try:
        response = requests.get(f"{base_url}/")
        print(f"主页状态码: {response.status_code}")
        if response.status_code == 200:
            print("✓ 主页访问正常")
        else:
            print("✗ 主页访问失败")
    except Exception as e:
        print(f"✗ 主页访问异常: {e}")
    
    # 测试API统计接口
    try:
        response = requests.get(f"{base_url}/api/stats")
        print(f"API统计接口状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("✓ API统计接口正常")
            print(f"  总告警数: {data.get('total_count', 0)}")
            print(f"  级别统计: {len(data.get('severity_stats', []))} 种")
            print(f"  类型统计: {len(data.get('type_stats', []))} 种")
        else:
            print("✗ API统计接口失败")
    except Exception as e:
        print(f"✗ API统计接口异常: {e}")
    
    # 测试统计页面
    try:
        response = requests.get(f"{base_url}/stats")
        print(f"统计页面状态码: {response.status_code}")
        if response.status_code == 200:
            print("✓ 统计页面访问正常")
        else:
            print("✗ 统计页面访问失败")
    except Exception as e:
        print(f"✗ 统计页面访问异常: {e}")
    
    # 测试告警详情页面（使用第一条记录）
    try:
        # 先获取一个告警ID
        import sqlite3
        conn = sqlite3.connect('zte_alarms.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM alarms LIMIT 1')
        result = cursor.fetchone()
        conn.close()
        
        if result:
            alarm_id = result[0]
            response = requests.get(f"{base_url}/alarm/{alarm_id}")
            print(f"告警详情页面状态码: {response.status_code}")
            if response.status_code == 200:
                print("✓ 告警详情页面访问正常")
            else:
                print("✗ 告警详情页面访问失败")
        else:
            print("! 数据库中没有告警记录，跳过详情页面测试")
    except Exception as e:
        print(f"✗ 告警详情页面访问异常: {e}")

if __name__ == "__main__":
    test_web_service()
