#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sqlite3
from bs4 import BeautifulSoup

def verify_detail_page():
    """验证详情页面是否显示了所有数据库字段"""
    
    # 获取数据库字段列表
    conn = sqlite3.connect('zte_alarms.db')
    cursor = conn.cursor()
    cursor.execute('PRAGMA table_info(alarms)')
    db_fields = [field[1] for field in cursor.fetchall()]
    
    # 获取第一条记录的ID
    cursor.execute('SELECT id FROM alarms LIMIT 1')
    alarm_id = cursor.fetchone()[0]
    conn.close()
    
    print(f"=== 验证告警详情页面 (ID: {alarm_id}) ===")
    print(f"数据库字段总数: {len(db_fields)}")
    
    # 获取详情页面HTML
    url = f"http://localhost:8080/alarm/{alarm_id}"
    response = requests.get(url)
    
    if response.status_code != 200:
        print(f"❌ 页面访问失败，状态码: {response.status_code}")
        return
    
    print(f"✅ 页面访问成功，内容大小: {len(response.text)} 字符")
    
    # 解析HTML
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # 查找所有英文字段名
    field_name_elements = soup.find_all('span', class_='field-name-en')
    displayed_fields = [elem.text.strip() for elem in field_name_elements]
    
    print(f"页面显示字段数: {len(displayed_fields)}")
    
    # 检查缺失的字段
    missing_fields = []
    for field in db_fields:
        if field not in displayed_fields:
            missing_fields.append(field)
    
    # 检查多余的字段
    extra_fields = []
    for field in displayed_fields:
        if field not in db_fields:
            extra_fields.append(field)
    
    print("\n=== 字段检查结果 ===")
    if not missing_fields:
        print("✅ 所有数据库字段都已显示")
    else:
        print(f"❌ 缺失字段 ({len(missing_fields)} 个):")
        for field in missing_fields:
            print(f"  - {field}")
    
    if extra_fields:
        print(f"\n⚠️  额外字段 ({len(extra_fields)} 个):")
        for field in extra_fields:
            print(f"  - {field}")
    
    # 检查分组
    section_headers = soup.find_all('div', class_='section-header')
    print(f"\n=== 页面分组 ({len(section_headers)} 个) ===")
    for header in section_headers:
        print(f"📋 {header.text.strip()}")
    
    # 检查JSON字段解析
    json_containers = soup.find_all('div', class_='json-container')
    print(f"\n=== JSON字段解析 ({len(json_containers)} 个) ===")
    if json_containers:
        print("✅ 发现JSON字段解析容器")
    else:
        print("⚠️  未发现JSON字段解析容器")
    
    print(f"\n=== 总结 ===")
    print(f"数据库字段: {len(db_fields)}")
    print(f"显示字段: {len(displayed_fields)}")
    print(f"缺失字段: {len(missing_fields)}")
    print(f"覆盖率: {((len(db_fields) - len(missing_fields)) / len(db_fields) * 100):.1f}%")

if __name__ == "__main__":
    try:
        verify_detail_page()
    except Exception as e:
        print(f"验证过程出错: {e}")
        import traceback
        traceback.print_exc()
