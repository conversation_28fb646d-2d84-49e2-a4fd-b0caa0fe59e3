{% extends "base.html" %}

{% block title %}告警列表 - 中兴网管告警查看器{% endblock %}
{% block page_title %}告警列表{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        搜索和筛选
    </div>
    <div class="card-body">
        <form method="GET" class="search-form">
            <div class="form-group">
                <label for="search">关键词搜索</label>
                <input type="text" id="search" name="search" class="form-control" 
                       placeholder="搜索告警标题、设备名称、告警描述..." value="{{ search }}">
            </div>
            
            <div class="form-group">
                <label for="severity">告警级别</label>
                <select id="severity" name="severity" class="form-control">
                    <option value="">全部级别</option>
                    {% for option in severity_options %}
                    <option value="{{ option }}" {% if option == severity %}selected{% endif %}>{{ option }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group">
                <label for="alarm_type">告警类型</label>
                <select id="alarm_type" name="alarm_type" class="form-control">
                    <option value="">全部类型</option>
                    {% for option in type_options %}
                    <option value="{{ option }}" {% if option == alarm_type %}selected{% endif %}>{{ option }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group">
                <label>&nbsp;</label>
                <button type="submit" class="btn">搜索</button>
                <a href="/" class="btn btn-secondary">重置</a>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>告警列表 (共 {{ total_count }} 条记录)</span>
            <div>
                <button onclick="toggleAllColumns()" class="btn btn-sm btn-secondary">显示/隐藏列</button>
                <button onclick="exportData()" class="btn btn-sm">导出数据</button>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if alarms %}
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th class="col-title">告警标题</th>
                        <th class="col-severity">级别</th>
                        <th class="col-status">状态</th>
                        <th class="col-device">设备名称</th>
                        <th class="col-ip">设备IP</th>
                        <th class="col-time">告警时间</th>
                        <th class="col-type">类型</th>
                        <th class="col-description">告警描述</th>
                        <th class="col-action">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for alarm in alarms %}
                    <tr>
                        <td title="{{ alarm.code_name or '未知' }}">
                            <strong>{{ alarm.code_name or '未知' }}</strong>
                        </td>
                        <td>
                            <span class="severity-{{ alarm.perceived_severity_name or '' }}">
                                {{ alarm.perceived_severity_name or '未知' }}
                            </span>
                        </td>
                        <td>{{ alarm.ack_state_name or '未知' }}</td>
                        <td title="{{ alarm.me_name or '' }}">
                            {{ alarm.me_name or '未知' }}
                        </td>
                        <td>{{ alarm.ne_ip or '-' }}</td>
                        <td title="{{ alarm.alarm_raised_time }}">
                            {{ alarm.alarm_raised_time_formatted or '未知' }}
                        </td>
                        <td>{{ alarm.alarm_type_name or '未知' }}</td>
                        <td title="{{ alarm.reason_name or '' }}">
                            {{ alarm.reason_name or '无描述' }}
                        </td>
                        <td>
                            <a href="/alarm/{{ alarm.id }}" class="btn btn-sm">详情</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if total_pages > 1 %}
        <div class="pagination">
            {% if page > 1 %}
            <a href="?page=1{% if search %}&search={{ search }}{% endif %}{% if severity %}&severity={{ severity }}{% endif %}{% if alarm_type %}&alarm_type={{ alarm_type }}{% endif %}">&laquo; 首页</a>
            <a href="?page={{ page - 1 }}{% if search %}&search={{ search }}{% endif %}{% if severity %}&severity={{ severity }}{% endif %}{% if alarm_type %}&alarm_type={{ alarm_type }}{% endif %}">&lsaquo; 上一页</a>
            {% endif %}
            
            {% for p in range([1, page - 2]|max, [total_pages + 1, page + 3]|min) %}
                {% if p == page %}
                <span class="pagination current">{{ p }}</span>
                {% else %}
                <a href="?page={{ p }}{% if search %}&search={{ search }}{% endif %}{% if severity %}&severity={{ severity }}{% endif %}{% if alarm_type %}&alarm_type={{ alarm_type }}{% endif %}">{{ p }}</a>
                {% endif %}
            {% endfor %}
            
            {% if page < total_pages %}
            <a href="?page={{ page + 1 }}{% if search %}&search={{ search }}{% endif %}{% if severity %}&severity={{ severity }}{% endif %}{% if alarm_type %}&alarm_type={{ alarm_type }}{% endif %}">下一页 &rsaquo;</a>
            <a href="?page={{ total_pages }}{% if search %}&search={{ search }}{% endif %}{% if severity %}&severity={{ severity }}{% endif %}{% if alarm_type %}&alarm_type={{ alarm_type }}{% endif %}">末页 &raquo;</a>
            {% endif %}
        </div>
        
        <div style="text-align: center; margin-top: 10px;" class="text-muted text-small">
            第 {{ page }} 页，共 {{ total_pages }} 页，显示第 {{ (page - 1) * 20 + 1 }} - {{ [page * 20, total_count]|min }} 条记录
        </div>
        {% endif %}
        
        {% else %}
        <div class="alert alert-info">
            没有找到符合条件的告警记录。
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 列宽拖拽功能
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('.table');
    if (!table) return;

    const headers = table.querySelectorAll('th');
    let isResizing = false;
    let currentHeader = null;
    let startX = 0;
    let startWidth = 0;

    headers.forEach(header => {
        // 创建拖拽手柄
        const resizeHandle = document.createElement('div');
        resizeHandle.style.cssText = `
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            cursor: col-resize;
            background: transparent;
            z-index: 1;
        `;

        header.style.position = 'relative';
        header.appendChild(resizeHandle);

        resizeHandle.addEventListener('mousedown', function(e) {
            isResizing = true;
            currentHeader = header;
            startX = e.pageX;
            startWidth = parseInt(window.getComputedStyle(header).width, 10);

            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            e.preventDefault();
        });

        resizeHandle.addEventListener('mouseenter', function() {
            resizeHandle.style.background = '#007bff';
        });

        resizeHandle.addEventListener('mouseleave', function() {
            if (!isResizing) {
                resizeHandle.style.background = 'transparent';
            }
        });
    });

    function handleMouseMove(e) {
        if (!isResizing) return;

        const width = startWidth + e.pageX - startX;
        if (width > 50) { // 最小宽度50px
            currentHeader.style.width = width + 'px';
        }
    }

    function handleMouseUp() {
        isResizing = false;
        currentHeader = null;

        // 恢复手柄颜色
        headers.forEach(header => {
            const handle = header.querySelector('div');
            if (handle) {
                handle.style.background = 'transparent';
            }
        });

        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
    }
});

// 列显示/隐藏功能
function toggleAllColumns() {
    const table = document.querySelector('.table');
    if (!table) return;

    const headers = table.querySelectorAll('th');
    const rows = table.querySelectorAll('tbody tr');

    // 创建列控制面板
    let panel = document.getElementById('column-panel');
    if (panel) {
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        return;
    }

    panel = document.createElement('div');
    panel.id = 'column-panel';
    panel.style.cssText = `
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1000;
        min-width: 200px;
    `;

    headers.forEach((header, index) => {
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = true;
        checkbox.id = `col-${index}`;

        const label = document.createElement('label');
        label.htmlFor = `col-${index}`;
        label.textContent = header.textContent;
        label.style.marginLeft = '5px';

        const div = document.createElement('div');
        div.style.marginBottom = '5px';
        div.appendChild(checkbox);
        div.appendChild(label);

        checkbox.addEventListener('change', function() {
            const columnIndex = index;
            header.style.display = this.checked ? '' : 'none';

            rows.forEach(row => {
                const cell = row.children[columnIndex];
                if (cell) {
                    cell.style.display = this.checked ? '' : 'none';
                }
            });
        });

        panel.appendChild(div);
    });

    const cardHeader = document.querySelector('.card-header');
    cardHeader.style.position = 'relative';
    cardHeader.appendChild(panel);
}

// 数据导出功能
function exportData() {
    const table = document.querySelector('.table');
    if (!table) return;

    let csv = '';
    const rows = table.querySelectorAll('tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('th, td');
        const rowData = [];

        cells.forEach(cell => {
            if (cell.style.display !== 'none') {
                let text = cell.textContent.trim();
                // 处理包含逗号的文本
                if (text.includes(',')) {
                    text = `"${text}"`;
                }
                rowData.push(text);
            }
        });

        csv += rowData.join(',') + '\n';
    });

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `告警数据_${new Date().toISOString().slice(0,10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 点击其他地方关闭列控制面板
document.addEventListener('click', function(e) {
    const panel = document.getElementById('column-panel');
    if (panel && !panel.contains(e.target) && !e.target.closest('button[onclick="toggleAllColumns()"]')) {
        panel.style.display = 'none';
    }
});
</script>
{% endblock %}
