#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def verify_database(db_file="zte_alarms.db"):
    """验证数据库中的数据"""
    
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # 统计总数
    cursor.execute('SELECT COUNT(*) FROM alarms')
    total_count = cursor.fetchone()[0]
    print(f"数据库中共有 {total_count} 条告警记录")
    
    # 查看前几条记录
    print("\n=== 前3条告警记录 ===")
    cursor.execute('''
        SELECT code_name, perceived_severity_name, me_name, alarm_raised_time 
        FROM alarms 
        ORDER BY alarm_raised_time DESC 
        LIMIT 3
    ''')
    
    for i, row in enumerate(cursor.fetchall(), 1):
        code_name, severity, me_name, alarm_time = row
        # 转换时间戳
        if alarm_time:
            dt = datetime.fromtimestamp(alarm_time / 1000)
            time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
        else:
            time_str = "未知"
        
        print(f"{i}. 告警: {code_name}")
        print(f"   级别: {severity}")
        print(f"   设备: {me_name[:60]}...")
        print(f"   时间: {time_str}")
        print()
    
    # 统计告警级别分布
    print("=== 告警级别分布 ===")
    cursor.execute('''
        SELECT perceived_severity_name, COUNT(*) 
        FROM alarms 
        GROUP BY perceived_severity_name 
        ORDER BY COUNT(*) DESC
    ''')
    
    for severity, count in cursor.fetchall():
        print(f"{severity}: {count} 条")
    
    # 统计告警类型分布
    print("\n=== 告警类型分布 ===")
    cursor.execute('''
        SELECT alarm_type_name, COUNT(*) 
        FROM alarms 
        GROUP BY alarm_type_name 
        ORDER BY COUNT(*) DESC
        LIMIT 10
    ''')
    
    for alarm_type, count in cursor.fetchall():
        print(f"{alarm_type}: {count} 条")
    
    conn.close()

if __name__ == "__main__":
    verify_database()
