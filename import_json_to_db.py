#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
from database_schema import create_database_schema, insert_alarm_data

def import_json_to_database(json_file, db_file="zte_alarms.db"):
    """将JSON文件中的告警数据导入到数据库"""
    
    print(f"开始导入 {json_file} 到数据库 {db_file}")
    
    # 创建数据库表结构
    create_database_schema(db_file)
    
    # 读取JSON数据
    with open(json_file, 'r', encoding='utf-8') as f:
        alarms = json.load(f)
    
    print(f"读取到 {len(alarms)} 条告警记录")
    
    # 导入数据
    start_time = time.time()
    success_count = 0
    error_count = 0
    
    for i, alarm in enumerate(alarms):
        try:
            insert_alarm_data(alarm, db_file)
            success_count += 1
            
            if (i + 1) % 100 == 0:
                print(f"已导入 {i + 1}/{len(alarms)} 条记录")
                
        except Exception as e:
            error_count += 1
            print(f"导入第 {i + 1} 条记录失败: {e}")
    
    elapsed_time = time.time() - start_time
    print(f"\n导入完成！")
    print(f"成功: {success_count} 条")
    print(f"失败: {error_count} 条")
    print(f"耗时: {elapsed_time:.2f} 秒")
    print(f"数据库文件: {db_file}")

if __name__ == "__main__":
    import_json_to_database("zte_active_alarms_raw_20250727_142332.json")
