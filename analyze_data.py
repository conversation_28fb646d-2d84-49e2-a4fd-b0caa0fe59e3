#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from datetime import datetime

# 读取数据
with open('zte_active_alarms_raw_20250727_142332.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"=== 中兴网管告警数据分析 ===")
print(f"总告警数量: {len(data)}")
print(f"每条告警字段数: {len(data[0])}")
print()

# 分析第一条告警
alarm = data[0]
print("=== 第一条告警详细信息 ===")
print(f"告警标题: {alarm.get('codename', '')}")
print(f"告警级别: {alarm.get('perceivedseverityname', '')}")
print(f"告警状态: {alarm.get('ackstatename', '')}")
print(f"设备名称: {alarm.get('mename', '')}")

# 时间戳转换
alarm_time = alarm.get('alarmraisedtime', 0)
if alarm_time:
    # 时间戳是毫秒级的
    dt = datetime.fromtimestamp(alarm_time / 1000)
    print(f"告警时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")

print(f"告警描述: {alarm.get('reasonname', '')}")
print(f"设备IP: {alarm.get('neip', '')}")
print(f"告警类型: {alarm.get('alarmtypename', '')}")

# 附加信息
additional = alarm.get('additionaltext', '')
if additional:
    print(f"附加信息: {additional[:200]}...")

print()
print("=== 告警级别统计 ===")
severity_count = {}
for alarm in data:
    severity = alarm.get('perceivedseverityname', '未知')
    severity_count[severity] = severity_count.get(severity, 0) + 1

for severity, count in severity_count.items():
    print(f"{severity}: {count} 条")

print()
print("=== 告警类型统计 ===")
type_count = {}
for alarm in data:
    alarm_type = alarm.get('alarmtypename', '未知')
    type_count[alarm_type] = type_count.get(alarm_type, 0) + 1

for alarm_type, count in sorted(type_count.items(), key=lambda x: x[1], reverse=True)[:10]:
    print(f"{alarm_type}: {count} 条")
