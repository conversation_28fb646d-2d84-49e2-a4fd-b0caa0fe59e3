#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import json
import math
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from typing import Dict, List, Any, Tuple

app = Flask(__name__)

# 配置
DB_FILE = "zte_alarms.db"
PAGE_SIZE = 20

print("Flask应用初始化完成")

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_FILE)
    conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
    return conn

def format_timestamp(timestamp):
    """格式化时间戳"""
    if timestamp:
        try:
            dt = datetime.fromtimestamp(timestamp / 1000)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return str(timestamp)
    return ""

def parse_json_field(json_str):
    """解析JSON字段"""
    if not json_str:
        return {}
    try:
        return json.loads(json_str)
    except:
        return {"value": json_str}

@app.route('/')
def index():
    """主页 - 告警列表"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    severity = request.args.get('severity', '', type=str)
    alarm_type = request.args.get('alarm_type', '', type=str)
    
    conn = get_db_connection()
    
    # 构建查询条件
    where_conditions = []
    params = []
    
    if search:
        where_conditions.append("(code_name LIKE ? OR me_name LIKE ? OR reason_name LIKE ?)")
        search_param = f"%{search}%"
        params.extend([search_param, search_param, search_param])
    
    if severity:
        where_conditions.append("perceived_severity_name = ?")
        params.append(severity)
    
    if alarm_type:
        where_conditions.append("alarm_type_name = ?")
        params.append(alarm_type)
    
    where_clause = " AND ".join(where_conditions)
    if where_clause:
        where_clause = "WHERE " + where_clause
    
    # 获取总数
    count_sql = f"SELECT COUNT(*) FROM alarms {where_clause}"
    total_count = conn.execute(count_sql, params).fetchone()[0]
    
    # 计算分页
    total_pages = math.ceil(total_count / PAGE_SIZE)
    offset = (page - 1) * PAGE_SIZE
    
    # 获取数据
    sql = f"""
        SELECT
            id, alarm_key, code_name, perceived_severity_name, ack_state_name,
            me_name, alarm_raised_time, alarm_type_name, reason_name, ne_ip,
            additional_text, position_name, alarm_code, res_type_name,
            clear_state_name, ack_user_id, comment_text
        FROM alarms
        {where_clause}
        ORDER BY alarm_raised_time DESC
        LIMIT ? OFFSET ?
    """
    
    params.extend([PAGE_SIZE, offset])
    alarms = conn.execute(sql, params).fetchall()
    
    # 格式化数据
    formatted_alarms = []
    for alarm in alarms:
        formatted_alarm = dict(alarm)
        formatted_alarm['alarm_raised_time_formatted'] = format_timestamp(alarm['alarm_raised_time'])
        formatted_alarm['additional_text_short'] = (alarm['additional_text'] or '')[:100] + '...' if len(alarm['additional_text'] or '') > 100 else (alarm['additional_text'] or '')
        formatted_alarms.append(formatted_alarm)
    
    # 获取筛选选项
    severity_options = conn.execute("SELECT DISTINCT perceived_severity_name FROM alarms WHERE perceived_severity_name IS NOT NULL ORDER BY perceived_severity_name").fetchall()
    type_options = conn.execute("SELECT DISTINCT alarm_type_name FROM alarms WHERE alarm_type_name IS NOT NULL ORDER BY alarm_type_name").fetchall()
    
    conn.close()
    
    return render_template('alarm_list.html', 
                         alarms=formatted_alarms,
                         page=page,
                         total_pages=total_pages,
                         total_count=total_count,
                         search=search,
                         severity=severity,
                         alarm_type=alarm_type,
                         severity_options=[row[0] for row in severity_options],
                         type_options=[row[0] for row in type_options])

@app.route('/alarm/<int:alarm_id>')
def alarm_detail(alarm_id):
    """告警详情页"""
    conn = get_db_connection()

    alarm = conn.execute("SELECT * FROM alarms WHERE id = ?", (alarm_id,)).fetchone()

    if not alarm:
        return "告警不存在", 404

    # 格式化数据
    alarm_dict = dict(alarm)

    # 格式化所有时间字段
    time_fields = [
        'alarm_raised_time', 'alarm_changed_time', 'server_time',
        'offset_alarm_raised_time', 'ack_time', 'comment_time'
    ]
    for field in time_fields:
        if alarm_dict.get(field):
            alarm_dict[f"{field}_formatted"] = format_timestamp(alarm_dict[field])

    # 解析所有JSON字段
    json_fields = [
        'ran_sdr_fm_native_param', 'ran_fm_alarm_object_name', 'ran_fm_alarm_service_id',
        'ran_fm_alarm_site_name', 'ran_fm_ne_virtualization', 'product_res_type',
        'ran_fm_alarm_dn', 'ran_fm_alarm_object_type', 'ran_fm_alarm_location',
        'ran_fm_alarm_object', 'ran_fm_alarm_board_type', 'ran_fm_alarm_object_id',
        'alarm_title_obj', 'maintain_status_obj', 'ne_plmns', 'operations'
    ]

    for field in json_fields:
        if alarm_dict.get(field):
            try:
                parsed_data = parse_json_field(alarm_dict[field])
                alarm_dict[f"{field}_parsed"] = parsed_data
            except Exception as e:
                print(f"解析JSON字段 {field} 失败: {e}")
                alarm_dict[f"{field}_parsed"] = None

    # 处理特殊的嵌套JSON字段（如ran_sdr_fm_native_param中的JSON字符串）
    if alarm_dict.get('ran_sdr_fm_native_param_parsed') and isinstance(alarm_dict['ran_sdr_fm_native_param_parsed'], dict):
        if 'value' in alarm_dict['ran_sdr_fm_native_param_parsed']:
            try:
                nested_json = json.loads(alarm_dict['ran_sdr_fm_native_param_parsed']['value'])
                alarm_dict['ran_sdr_fm_native_param_parsed']['nested_data'] = nested_json
            except:
                pass

    conn.close()

    return render_template('alarm_detail.html', alarm=alarm_dict)

@app.route('/api/stats')
def api_stats():
    """API - 获取统计信息"""
    conn = get_db_connection()
    
    # 总数统计
    total_count = conn.execute("SELECT COUNT(*) FROM alarms").fetchone()[0]
    
    # 级别统计
    severity_stats = conn.execute("""
        SELECT perceived_severity_name, COUNT(*) as count
        FROM alarms 
        WHERE perceived_severity_name IS NOT NULL
        GROUP BY perceived_severity_name 
        ORDER BY count DESC
    """).fetchall()
    
    # 类型统计
    type_stats = conn.execute("""
        SELECT alarm_type_name, COUNT(*) as count
        FROM alarms 
        WHERE alarm_type_name IS NOT NULL
        GROUP BY alarm_type_name 
        ORDER BY count DESC
        LIMIT 10
    """).fetchall()
    
    # 状态统计
    ack_stats = conn.execute("""
        SELECT ack_state_name, COUNT(*) as count
        FROM alarms 
        WHERE ack_state_name IS NOT NULL
        GROUP BY ack_state_name 
        ORDER BY count DESC
    """).fetchall()
    
    conn.close()
    
    return jsonify({
        'total_count': total_count,
        'severity_stats': [{'name': row[0], 'count': row[1]} for row in severity_stats],
        'type_stats': [{'name': row[0], 'count': row[1]} for row in type_stats],
        'ack_stats': [{'name': row[0], 'count': row[1]} for row in ack_stats]
    })

@app.route('/stats')
def stats():
    """统计页面"""
    return render_template('stats.html')

if __name__ == '__main__':
    print("启动中兴告警查看器...")
    print(f"数据库文件: {DB_FILE}")
    print("访问地址: http://localhost:8080")
    app.run(debug=False, host='0.0.0.0', port=8080)
