#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import json
import math
from datetime import datetime
from flask import Flask, request, jsonify
from typing import Dict, List, Any, Tuple

app = Flask(__name__)

# 配置
DB_FILE = "zte_alarms.db"
PAGE_SIZE = 20

print("Flask应用初始化完成")

def get_base_html(title, page_title, content, extra_css="", extra_js=""):
    """生成基础HTML页面"""
    base_css = """
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f5f5; color: #333; line-height: 1.6; }
        .container { width: 100%; margin: 0; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 0; margin-bottom: 30px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header h1 { text-align: center; font-size: 2.2em; font-weight: 300; }
        .nav { text-align: center; margin-top: 15px; }
        .nav a { color: white; text-decoration: none; margin: 0 20px; padding: 8px 16px; border-radius: 4px; transition: background-color 0.3s; }
        .nav a:hover { background-color: rgba(255,255,255,0.2); }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; overflow: hidden; }
        .card-header { background-color: #f8f9fa; padding: 15px 20px; border-bottom: 1px solid #dee2e6; font-weight: 600; color: #495057; }
        .card-body { padding: 20px; }
        .btn { display: inline-block; padding: 8px 16px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; transition: background-color 0.3s; font-size: 14px; }
        .btn:hover { background-color: #0056b3; }
        .btn-sm { padding: 4px 8px; font-size: 12px; }
        .btn-secondary { background-color: #6c757d; }
        .btn-secondary:hover { background-color: #545b62; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 500; }
        .form-control { width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px; }
        .form-control:focus { outline: none; border-color: #80bdff; box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25); }
        .table-container { width: 100%; overflow-x: auto; margin-top: 10px; }
        .table { width: 100%; border-collapse: collapse; table-layout: fixed; min-width: 1200px; }
        .table th, .table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #dee2e6; border-right: 1px solid #dee2e6; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; position: relative; }
        .table th { background-color: #f8f9fa; font-weight: 600; color: #495057; resize: horizontal; user-select: none; }
        .table tbody tr:hover { background-color: #f8f9fa; }
        .table th { position: relative; }
        .table th::after { content: ''; position: absolute; top: 0; right: 0; width: 4px; height: 100%; cursor: col-resize; background: transparent; }
        .table th:hover::after { background: #007bff; }
        .col-title { width: 200px; } .col-severity { width: 80px; } .col-status { width: 80px; } .col-device { width: 250px; } .col-ip { width: 120px; } .col-time { width: 150px; } .col-type { width: 120px; } .col-action { width: 80px; } .col-description { width: 300px; }
        .severity-严重 { color: #dc3545; font-weight: bold; } .severity-主要 { color: #fd7e14; font-weight: bold; } .severity-次要 { color: #ffc107; font-weight: bold; } .severity-警告 { color: #28a745; }
        .pagination { display: flex; justify-content: center; align-items: center; margin-top: 20px; gap: 10px; }
        .pagination a { padding: 8px 12px; text-decoration: none; border: 1px solid #dee2e6; border-radius: 4px; color: #007bff; }
        .pagination a:hover { background-color: #e9ecef; }
        .pagination .current { background-color: #007bff; color: white; border-color: #007bff; }
        .search-form { display: flex; gap: 10px; align-items: end; flex-wrap: wrap; margin-bottom: 20px; }
        .search-form .form-group { margin-bottom: 0; min-width: 150px; }
        .alert { padding: 12px 16px; border-radius: 4px; margin-bottom: 20px; }
        .alert-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .text-muted { color: #6c757d; } .text-small { font-size: 0.875em; }
        @media (max-width: 768px) { .container { padding: 10px; } .search-form { flex-direction: column; } .search-form .form-group { width: 100%; } .table { font-size: 11px; min-width: 800px; } .table th, .table td { padding: 6px 8px; } .col-title { width: 150px; } .col-device { width: 200px; } .col-description { width: 200px; } }
    """

    return f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>{base_css}{extra_css}</style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>{page_title}</h1>
            <div class="nav">
                <a href="/">告警列表</a>
                <a href="/stats">统计信息</a>
            </div>
        </div>
    </div>
    <div class="container">{content}</div>
    {extra_js}
</body>
</html>'''

# 告警列表页面模板
ALARM_LIST_CONTENT = '''
<div class="card">
    <div class="card-header">
        搜索和筛选
    </div>
    <div class="card-body">
        <form method="GET" class="search-form">
            <div class="form-group">
                <label for="search">关键词搜索</label>
                <input type="text" id="search" name="search" class="form-control"
                       placeholder="搜索告警标题、设备名称、告警描述..." value="{search}">
            </div>

            <div class="form-group">
                <label for="severity">告警级别</label>
                <select id="severity" name="severity" class="form-control">
                    <option value="">全部级别</option>
                    {severity_options}
                </select>
            </div>

            <div class="form-group">
                <label for="alarm_type">告警类型</label>
                <select id="alarm_type" name="alarm_type" class="form-control">
                    <option value="">全部类型</option>
                    {type_options}
                </select>
            </div>

            <div class="form-group">
                <label>&nbsp;</label>
                <button type="submit" class="btn">搜索</button>
                <a href="/" class="btn btn-secondary">重置</a>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>告警列表 (共 {total_count} 条记录)</span>
            <div>
                <button onclick="toggleAllColumns()" class="btn btn-sm btn-secondary">显示/隐藏列</button>
                <button onclick="exportData()" class="btn btn-sm">导出数据</button>
            </div>
        </div>
    </div>
    <div class="card-body">
        {alarm_table}
        {pagination}
    </div>
</div>
'''

ALARM_LIST_JS = '''
<script>
// 列宽拖拽功能
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('.table');
    if (!table) return;

    const headers = table.querySelectorAll('th');
    let isResizing = false;
    let currentHeader = null;
    let startX = 0;
    let startWidth = 0;

    headers.forEach(header => {
        // 创建拖拽手柄
        const resizeHandle = document.createElement('div');
        resizeHandle.style.cssText = `
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            cursor: col-resize;
            background: transparent;
            z-index: 1;
        `;

        header.style.position = 'relative';
        header.appendChild(resizeHandle);

        resizeHandle.addEventListener('mousedown', function(e) {
            isResizing = true;
            currentHeader = header;
            startX = e.pageX;
            startWidth = parseInt(window.getComputedStyle(header).width, 10);

            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            e.preventDefault();
        });

        resizeHandle.addEventListener('mouseenter', function() {
            resizeHandle.style.background = '#007bff';
        });

        resizeHandle.addEventListener('mouseleave', function() {
            if (!isResizing) {
                resizeHandle.style.background = 'transparent';
            }
        });
    });

    function handleMouseMove(e) {
        if (!isResizing) return;

        const width = startWidth + e.pageX - startX;
        if (width > 50) { // 最小宽度50px
            currentHeader.style.width = width + 'px';
        }
    }

    function handleMouseUp() {
        isResizing = false;
        currentHeader = null;

        // 恢复手柄颜色
        headers.forEach(header => {
            const handle = header.querySelector('div');
            if (handle) {
                handle.style.background = 'transparent';
            }
        });

        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
    }
});

// 列显示/隐藏功能
function toggleAllColumns() {
    const table = document.querySelector('.table');
    if (!table) return;

    const headers = table.querySelectorAll('th');
    const rows = table.querySelectorAll('tbody tr');

    // 创建列控制面板
    let panel = document.getElementById('column-panel');
    if (panel) {
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        return;
    }

    panel = document.createElement('div');
    panel.id = 'column-panel';
    panel.style.cssText = `
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1000;
        min-width: 200px;
    `;

    headers.forEach((header, index) => {
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = true;
        checkbox.id = `col-${index}`;

        const label = document.createElement('label');
        label.htmlFor = `col-${index}`;
        label.textContent = header.textContent;
        label.style.marginLeft = '5px';

        const div = document.createElement('div');
        div.style.marginBottom = '5px';
        div.appendChild(checkbox);
        div.appendChild(label);

        checkbox.addEventListener('change', function() {
            const columnIndex = index;
            header.style.display = this.checked ? '' : 'none';

            rows.forEach(row => {
                const cell = row.children[columnIndex];
                if (cell) {
                    cell.style.display = this.checked ? '' : 'none';
                }
            });
        });

        panel.appendChild(div);
    });

    const cardHeader = document.querySelector('.card-header');
    cardHeader.style.position = 'relative';
    cardHeader.appendChild(panel);
}

// 数据导出功能
function exportData() {
    const table = document.querySelector('.table');
    if (!table) return;

    let csv = '';
    const rows = table.querySelectorAll('tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('th, td');
        const rowData = [];

        cells.forEach(cell => {
            if (cell.style.display !== 'none') {
                let text = cell.textContent.trim();
                // 处理包含逗号的文本
                if (text.includes(',')) {
                    text = `"${text}"`;
                }
                rowData.push(text);
            }
        });

        csv += rowData.join(',') + '\\n';
    });

    // 创建下载链接
    const blob = new Blob(['\\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `告警数据_${new Date().toISOString().slice(0,10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 点击其他地方关闭列控制面板
document.addEventListener('click', function(e) {
    const panel = document.getElementById('column-panel');
    if (panel && !panel.contains(e.target) && !e.target.closest('button[onclick="toggleAllColumns()"]')) {
        panel.style.display = 'none';
    }
});
</script>
'''

# 统计页面模板
STATS_CONTENT = '''
<div id="loading" style="text-align: center; padding: 50px;">
    <p>正在加载统计数据...</p>
</div>

<div id="stats-content" style="display: none;">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number" id="total-count">-</div>
            <div class="stat-label">告警总数</div>
        </div>

        <div class="stat-card">
            <div class="stat-number" id="critical-count" style="color: #dc3545;">-</div>
            <div class="stat-label">严重告警</div>
        </div>

        <div class="stat-card">
            <div class="stat-number" id="major-count" style="color: #fd7e14;">-</div>
            <div class="stat-label">主要告警</div>
        </div>

        <div class="stat-card">
            <div class="stat-number" id="minor-count" style="color: #ffc107;">-</div>
            <div class="stat-label">次要告警</div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            告警级别分布
        </div>
        <div class="card-body">
            <div id="severity-stats"></div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            告警类型分布 (Top 10)
        </div>
        <div class="card-body">
            <div id="type-stats"></div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            确认状态分布
        </div>
        <div class="card-body">
            <div id="ack-stats"></div>
        </div>
    </div>
</div>
'''

STATS_CSS = '''
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 20px;
        text-align: center;
    }

    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        color: #007bff;
        margin-bottom: 10px;
    }

    .stat-label {
        color: #6c757d;
        font-size: 1.1em;
    }

    .chart-container {
        height: 300px;
        margin: 20px 0;
    }

    .stats-table {
        width: 100%;
        border-collapse: collapse;
    }

    .stats-table th,
    .stats-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }

    .stats-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .progress-bar {
        background-color: #e9ecef;
        border-radius: 4px;
        height: 20px;
        overflow: hidden;
        margin-left: 10px;
        flex: 1;
    }

    .progress-fill {
        height: 100%;
        background-color: #007bff;
        transition: width 0.3s ease;
    }

    .stats-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .stats-label {
        min-width: 120px;
        font-weight: 500;
    }

    .stats-count {
        min-width: 60px;
        text-align: right;
        margin-right: 10px;
    }
'''

STATS_JS = '''
<script>
function loadStats() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            // 更新总数统计
            document.getElementById('total-count').textContent = data.total_count.toLocaleString();

            // 更新级别统计
            const severityMap = {};
            data.severity_stats.forEach(item => {
                severityMap[item.name] = item.count;
            });

            document.getElementById('critical-count').textContent = (severityMap['严重'] || 0).toLocaleString();
            document.getElementById('major-count').textContent = (severityMap['主要'] || 0).toLocaleString();
            document.getElementById('minor-count').textContent = (severityMap['次要'] || 0).toLocaleString();

            // 渲染级别分布
            renderStatsChart('severity-stats', data.severity_stats, data.total_count);

            // 渲染类型分布
            renderStatsChart('type-stats', data.type_stats, data.total_count);

            // 渲染确认状态分布
            renderStatsChart('ack-stats', data.ack_stats, data.total_count);

            // 显示内容，隐藏加载提示
            document.getElementById('loading').style.display = 'none';
            document.getElementById('stats-content').style.display = 'block';
        })
        .catch(error => {
            console.error('加载统计数据失败:', error);
            document.getElementById('loading').innerHTML = '<p style="color: red;">加载统计数据失败</p>';
        });
}

function renderStatsChart(containerId, data, total) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';

    data.forEach(item => {
        const percentage = ((item.count / total) * 100).toFixed(1);

        const row = document.createElement('div');
        row.className = 'stats-row';

        row.innerHTML = `
            <div class="stats-label">${item.name}</div>
            <div class="stats-count">${item.count.toLocaleString()}</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${percentage}%"></div>
            </div>
            <div style="margin-left: 10px; min-width: 50px; font-size: 0.9em; color: #6c757d;">
                ${percentage}%
            </div>
        `;

        container.appendChild(row);
    });
}

// 页面加载完成后获取统计数据
document.addEventListener('DOMContentLoaded', loadStats);
</script>
'''

# 告警详情页面模板
ALARM_DETAIL_CONTENT = '''
<div style="margin-bottom: 20px;">
    <a href="/" class="btn btn-secondary">&laquo; 返回列表</a>
    <button onclick="toggleAllSections()" class="btn btn-secondary">展开/收起所有</button>
    <button onclick="exportAlarmData()" class="btn">导出详情</button>
</div>

{alarm_sections}
'''

ALARM_DETAIL_CSS = '''
    .field-group {
        margin-bottom: 30px;
    }

    .field-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }

    .field-table th,
    .field-table td {
        padding: 8px 12px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
        vertical-align: top;
    }

    .field-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        width: 200px;
    }

    .field-name {
        font-weight: 500;
        color: #495057;
    }

    .field-name-en {
        font-size: 0.85em;
        color: #6c757d;
        font-weight: normal;
    }

    .field-value {
        word-break: break-all;
        max-width: 600px;
    }

    .json-container {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 10px;
        margin: 5px 0;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        max-height: 200px;
        overflow-y: auto;
    }

    .json-key {
        color: #0066cc;
        font-weight: bold;
    }

    .json-value {
        color: #009900;
    }

    .null-value {
        color: #999;
        font-style: italic;
    }

    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 10px 15px;
        border-radius: 6px;
        margin-bottom: 15px;
        font-weight: 600;
    }
'''

ALARM_DETAIL_JS = '''
<script>
// 展开/收起所有部分
function toggleAllSections() {
    const sections = document.querySelectorAll('.field-group');
    const firstSection = sections[0];
    const isCollapsed = firstSection.style.display === 'none';

    sections.forEach(section => {
        section.style.display = isCollapsed ? 'block' : 'none';
    });
}

// 导出告警详情数据
function exportAlarmData() {
    const alarmData = {};

    // 收集所有字段数据
    const tables = document.querySelectorAll('.field-table');
    tables.forEach(table => {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            const th = row.querySelector('th');
            const td = row.querySelector('td');
            if (th && td) {
                const fieldNameEn = th.querySelector('.field-name-en');
                const fieldName = th.querySelector('.field-name');
                if (fieldNameEn && fieldName) {
                    const key = fieldNameEn.textContent.trim();
                    const value = td.textContent.trim();
                    alarmData[key] = value;
                }
            }
        });
    });

    // 创建JSON文件并下载
    const jsonStr = JSON.stringify(alarmData, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `alarm_detail_${alarmData.id || 'unknown'}_${new Date().toISOString().slice(0,10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 为每个部分添加点击展开/收起功能
    const headers = document.querySelectorAll('.section-header');
    headers.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const cardBody = this.nextElementSibling;
            if (cardBody) {
                const isHidden = cardBody.style.display === 'none';
                cardBody.style.display = isHidden ? 'block' : 'none';

                // 添加展开/收起图标
                const icon = this.querySelector('.toggle-icon') || document.createElement('span');
                icon.className = 'toggle-icon';
                icon.style.float = 'right';
                icon.textContent = isHidden ? '▼' : '▲';
                if (!this.querySelector('.toggle-icon')) {
                    this.appendChild(icon);
                }
            }
        });

        // 初始添加图标
        const icon = document.createElement('span');
        icon.className = 'toggle-icon';
        icon.style.float = 'right';
        icon.textContent = '▼';
        header.appendChild(icon);
    });

    // 添加搜索功能
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = '搜索字段名或值...';
    searchInput.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 8px 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        z-index: 1000;
        background: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    `;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('.field-table tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    document.body.appendChild(searchInput);
});
</script>
'''

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_FILE)
    conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
    return conn

def format_timestamp(timestamp):
    """格式化时间戳"""
    if timestamp:
        try:
            dt = datetime.fromtimestamp(timestamp / 1000)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return str(timestamp)
    return ""

def parse_json_field(json_str):
    """解析JSON字段"""
    if not json_str:
        return {}
    try:
        return json.loads(json_str)
    except:
        return {"value": json_str}

@app.route('/')
def index():
    """主页 - 告警列表"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    severity = request.args.get('severity', '', type=str)
    alarm_type = request.args.get('alarm_type', '', type=str)
    
    conn = get_db_connection()
    
    # 构建查询条件
    where_conditions = []
    params = []
    
    if search:
        where_conditions.append("(code_name LIKE ? OR me_name LIKE ? OR reason_name LIKE ?)")
        search_param = f"%{search}%"
        params.extend([search_param, search_param, search_param])
    
    if severity:
        where_conditions.append("perceived_severity_name = ?")
        params.append(severity)
    
    if alarm_type:
        where_conditions.append("alarm_type_name = ?")
        params.append(alarm_type)
    
    where_clause = " AND ".join(where_conditions)
    if where_clause:
        where_clause = "WHERE " + where_clause
    
    # 获取总数
    count_sql = f"SELECT COUNT(*) FROM alarms {where_clause}"
    total_count = conn.execute(count_sql, params).fetchone()[0]
    
    # 计算分页
    total_pages = math.ceil(total_count / PAGE_SIZE)
    offset = (page - 1) * PAGE_SIZE
    
    # 获取数据
    sql = f"""
        SELECT
            id, alarm_key, code_name, perceived_severity_name, ack_state_name,
            me_name, alarm_raised_time, alarm_type_name, reason_name, ne_ip,
            additional_text, position_name, alarm_code, res_type_name,
            clear_state_name, ack_user_id, comment_text
        FROM alarms
        {where_clause}
        ORDER BY alarm_raised_time DESC
        LIMIT ? OFFSET ?
    """
    
    params.extend([PAGE_SIZE, offset])
    alarms = conn.execute(sql, params).fetchall()
    
    # 格式化数据
    formatted_alarms = []
    for alarm in alarms:
        formatted_alarm = dict(alarm)
        formatted_alarm['alarm_raised_time_formatted'] = format_timestamp(alarm['alarm_raised_time'])
        formatted_alarm['additional_text_short'] = (alarm['additional_text'] or '')[:100] + '...' if len(alarm['additional_text'] or '') > 100 else (alarm['additional_text'] or '')
        formatted_alarms.append(formatted_alarm)
    
    # 获取筛选选项
    severity_options = conn.execute("SELECT DISTINCT perceived_severity_name FROM alarms WHERE perceived_severity_name IS NOT NULL ORDER BY perceived_severity_name").fetchall()
    type_options = conn.execute("SELECT DISTINCT alarm_type_name FROM alarms WHERE alarm_type_name IS NOT NULL ORDER BY alarm_type_name").fetchall()
    
    conn.close()

    # 生成选项HTML
    severity_options_html = ""
    for option in [row[0] for row in severity_options]:
        selected = 'selected' if option == severity else ''
        severity_options_html += f'<option value="{option}" {selected}>{option}</option>'

    type_options_html = ""
    for option in [row[0] for row in type_options]:
        selected = 'selected' if option == alarm_type else ''
        type_options_html += f'<option value="{option}" {selected}>{option}</option>'

    # 生成告警表格HTML
    if formatted_alarms:
        alarm_table_html = '''
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th class="col-title">告警标题</th>
                        <th class="col-severity">级别</th>
                        <th class="col-status">状态</th>
                        <th class="col-device">设备名称</th>
                        <th class="col-ip">设备IP</th>
                        <th class="col-time">告警时间</th>
                        <th class="col-type">类型</th>
                        <th class="col-description">告警描述</th>
                        <th class="col-action">操作</th>
                    </tr>
                </thead>
                <tbody>'''

        for alarm in formatted_alarms:
            alarm_table_html += f'''
                    <tr>
                        <td title="{alarm.get('code_name', '未知')}">
                            <strong>{alarm.get('code_name', '未知')}</strong>
                        </td>
                        <td>
                            <span class="severity-{alarm.get('perceived_severity_name', '')}">
                                {alarm.get('perceived_severity_name', '未知')}
                            </span>
                        </td>
                        <td>{alarm.get('ack_state_name', '未知')}</td>
                        <td title="{alarm.get('me_name', '')}">{alarm.get('me_name', '未知')}</td>
                        <td>{alarm.get('ne_ip', '-')}</td>
                        <td title="{alarm.get('alarm_raised_time', '')}">{alarm.get('alarm_raised_time_formatted', '未知')}</td>
                        <td>{alarm.get('alarm_type_name', '未知')}</td>
                        <td title="{alarm.get('reason_name', '')}">{alarm.get('reason_name', '无描述')}</td>
                        <td>
                            <a href="/alarm/{alarm.get('id')}" class="btn btn-sm">详情</a>
                        </td>
                    </tr>'''

        alarm_table_html += '''
                </tbody>
            </table>
        </div>'''
    else:
        alarm_table_html = '<div class="alert alert-info">没有找到符合条件的告警记录。</div>'

    # 生成分页HTML
    pagination_html = ""
    if total_pages > 1:
        pagination_html = '<div class="pagination">'

        # 构建查询参数
        query_params = []
        if search:
            query_params.append(f"search={search}")
        if severity:
            query_params.append(f"severity={severity}")
        if alarm_type:
            query_params.append(f"alarm_type={alarm_type}")
        query_string = "&" + "&".join(query_params) if query_params else ""

        if page > 1:
            pagination_html += f'<a href="?page=1{query_string}">&laquo; 首页</a>'
            pagination_html += f'<a href="?page={page - 1}{query_string}">&lsaquo; 上一页</a>'

        # 页码范围
        start_page = max(1, page - 2)
        end_page = min(total_pages + 1, page + 3)

        for p in range(start_page, end_page):
            if p == page:
                pagination_html += f'<span class="pagination current">{p}</span>'
            else:
                pagination_html += f'<a href="?page={p}{query_string}">{p}</a>'

        if page < total_pages:
            pagination_html += f'<a href="?page={page + 1}{query_string}">下一页 &rsaquo;</a>'
            pagination_html += f'<a href="?page={total_pages}{query_string}">末页 &raquo;</a>'

        pagination_html += '</div>'
        pagination_html += f'''
        <div style="text-align: center; margin-top: 10px;" class="text-muted text-small">
            第 {page} 页，共 {total_pages} 页，显示第 {(page - 1) * 20 + 1} - {min(page * 20, total_count)} 条记录
        </div>'''

    # 渲染页面
    content = ALARM_LIST_CONTENT.format(
        search=search,
        severity_options=severity_options_html,
        type_options=type_options_html,
        total_count=total_count,
        alarm_table=alarm_table_html,
        pagination=pagination_html
    )

    return get_base_html(
        title="告警列表 - 中兴网管告警查看器",
        page_title="告警列表",
        content=content,
        extra_js=ALARM_LIST_JS
    )

@app.route('/alarm/<int:alarm_id>')
def alarm_detail(alarm_id):
    """告警详情页"""
    conn = get_db_connection()

    alarm = conn.execute("SELECT * FROM alarms WHERE id = ?", (alarm_id,)).fetchone()

    if not alarm:
        return "告警不存在", 404

    # 格式化数据
    alarm_dict = dict(alarm)

    # 格式化所有时间字段
    time_fields = [
        'alarm_raised_time', 'alarm_changed_time', 'server_time',
        'offset_alarm_raised_time', 'ack_time', 'comment_time'
    ]
    for field in time_fields:
        if alarm_dict.get(field):
            alarm_dict[f"{field}_formatted"] = format_timestamp(alarm_dict[field])

    # 解析所有JSON字段
    json_fields = [
        'ran_sdr_fm_native_param', 'ran_fm_alarm_object_name', 'ran_fm_alarm_service_id',
        'ran_fm_alarm_site_name', 'ran_fm_ne_virtualization', 'product_res_type',
        'ran_fm_alarm_dn', 'ran_fm_alarm_object_type', 'ran_fm_alarm_location',
        'ran_fm_alarm_object', 'ran_fm_alarm_board_type', 'ran_fm_alarm_object_id',
        'alarm_title_obj', 'maintain_status_obj', 'ne_plmns', 'operations'
    ]

    for field in json_fields:
        if alarm_dict.get(field):
            try:
                parsed_data = parse_json_field(alarm_dict[field])
                alarm_dict[f"{field}_parsed"] = parsed_data
            except Exception as e:
                print(f"解析JSON字段 {field} 失败: {e}")
                alarm_dict[f"{field}_parsed"] = None

    # 处理特殊的嵌套JSON字段（如ran_sdr_fm_native_param中的JSON字符串）
    if alarm_dict.get('ran_sdr_fm_native_param_parsed') and isinstance(alarm_dict['ran_sdr_fm_native_param_parsed'], dict):
        if 'value' in alarm_dict['ran_sdr_fm_native_param_parsed']:
            try:
                nested_json = json.loads(alarm_dict['ran_sdr_fm_native_param_parsed']['value'])
                alarm_dict['ran_sdr_fm_native_param_parsed']['nested_data'] = nested_json
            except:
                pass

    conn.close()

    # 生成告警详情各个部分的HTML
    def generate_field_section(title, icon, fields):
        section_html = f'''
<div class="card field-group">
    <div class="section-header">
        {icon} {title}
    </div>
    <div class="card-body">
        <table class="field-table">'''

        for field_info in fields:
            field_key = field_info['key']
            field_name_cn = field_info['name_cn']
            field_name_en = field_info['name_en']
            field_value = alarm_dict.get(field_key, '-')

            # 特殊处理
            if field_info.get('is_time') and field_value and field_value != '-':
                formatted_time = alarm_dict.get(f"{field_key}_formatted", field_value)
                field_value = f'{formatted_time}<br><small class="text-muted">原始值: {field_value}</small>'
            elif field_info.get('is_boolean'):
                if field_value is not None:
                    field_value = '是' if field_value else '否'
                else:
                    field_value = '-'
            elif field_info.get('is_json'):
                parsed_key = f"{field_key}_parsed"
                if alarm_dict.get(parsed_key):
                    json_data = alarm_dict[parsed_key]
                    if isinstance(json_data, dict):
                        json_html = '<div class="json-container">'
                        for k, v in json_data.items():
                            json_html += f'<div><span class="json-key">{k}:</span> <span class="json-value">{v}</span></div>'
                        json_html += '</div>'
                        field_value = json_html
                    elif isinstance(json_data, list):
                        json_html = '<div class="json-container">'
                        for item in json_data:
                            if isinstance(item, dict):
                                json_html += f'<div style="margin-bottom: 5px; padding: 5px; background: #fff; border: 1px solid #ddd; border-radius: 3px;">'
                                for k, v in item.items():
                                    json_html += f'<span class="json-key">{k}:</span> <span class="json-value">{v}</span><br>'
                                json_html += '</div>'
                            else:
                                json_html += f'<div><span class="json-value">{item}</span></div>'
                        json_html += '</div>'
                        field_value = json_html
                else:
                    field_value = f'<span class="null-value">{field_value or "-"}</span>'
            elif field_info.get('is_severity'):
                field_value = f'<span class="severity-{field_value or ""}">{field_value or "-"}</span>'

            if field_info.get('is_strong'):
                field_value = f'<strong>{field_value}</strong>'

            section_html += f'''
            <tr>
                <th><span class="field-name">{field_name_cn}</span><br><span class="field-name-en">{field_name_en}</span></th>
                <td class="field-value">{field_value}</td>
            </tr>'''

        section_html += '''
        </table>
    </div>
</div>'''
        return section_html

    # 定义各个部分的字段
    basic_fields = [
        {'key': 'id', 'name_cn': '记录ID', 'name_en': 'id'},
        {'key': 'alarm_key', 'name_cn': '告警键', 'name_en': 'alarm_key'},
        {'key': 'sequence', 'name_cn': '序列号', 'name_en': 'sequence'},
        {'key': 'alarm_code', 'name_cn': '告警代码', 'name_en': 'alarm_code'},
        {'key': 'code_name', 'name_cn': '告警标题', 'name_en': 'code_name', 'is_strong': True},
        {'key': 'alarm_title', 'name_cn': '告警标题对象', 'name_en': 'alarm_title'},
        {'key': 'alarm_type', 'name_cn': '告警类型', 'name_en': 'alarm_type'},
        {'key': 'alarm_type_name', 'name_cn': '告警类型名称', 'name_en': 'alarm_type_name'},
        {'key': 'reason_code', 'name_cn': '原因代码', 'name_en': 'reason_code'},
        {'key': 'reason_name', 'name_cn': '原因描述', 'name_en': 'reason_name'},
        {'key': 'additional_text', 'name_cn': '附加信息', 'name_en': 'additional_text'},
    ]

    severity_fields = [
        {'key': 'perceived_severity', 'name_cn': '感知严重性', 'name_en': 'perceived_severity'},
        {'key': 'perceived_severity_name', 'name_cn': '告警级别', 'name_en': 'perceived_severity_name', 'is_severity': True},
        {'key': 'ack_state', 'name_cn': '确认状态', 'name_en': 'ack_state'},
        {'key': 'ack_state_name', 'name_cn': '确认状态名称', 'name_en': 'ack_state_name'},
        {'key': 'ack_user_id', 'name_cn': '确认用户ID', 'name_en': 'ack_user_id'},
        {'key': 'ack_time', 'name_cn': '确认时间', 'name_en': 'ack_time', 'is_time': True},
        {'key': 'clear_state', 'name_cn': '清除状态', 'name_en': 'clear_state'},
        {'key': 'clear_state_name', 'name_cn': '清除状态名称', 'name_en': 'clear_state_name'},
    ]

    time_fields = [
        {'key': 'alarm_raised_time', 'name_cn': '告警发生时间', 'name_en': 'alarm_raised_time', 'is_time': True},
        {'key': 'alarm_changed_time', 'name_cn': '告警变更时间', 'name_en': 'alarm_changed_time', 'is_time': True},
        {'key': 'server_time', 'name_cn': '服务器时间', 'name_en': 'server_time', 'is_time': True},
        {'key': 'offset_alarm_raised_time', 'name_cn': '偏移告警时间', 'name_en': 'offset_alarm_raised_time', 'is_time': True},
        {'key': 'timezone_id', 'name_cn': '时区ID', 'name_en': 'timezone_id'},
        {'key': 'timezone_offset', 'name_cn': '时区偏移', 'name_en': 'timezone_offset'},
    ]

    device_fields = [
        {'key': 'me', 'name_cn': '管理元素', 'name_en': 'me'},
        {'key': 'me_name', 'name_cn': '设备名称', 'name_en': 'me_name'},
        {'key': 'ne_ip', 'name_cn': '网元IP', 'name_en': 'ne_ip'},
        {'key': 'nbi_id', 'name_cn': 'NBI ID', 'name_en': 'nbi_id'},
        {'key': 'moc', 'name_cn': 'MOC', 'name_en': 'moc'},
        {'key': 'moc_name', 'name_cn': 'MOC名称', 'name_en': 'moc_name'},
        {'key': 'position', 'name_cn': '位置', 'name_en': 'position'},
        {'key': 'position_name', 'name_cn': '位置名称', 'name_en': 'position_name'},
        {'key': 'res_type', 'name_cn': '资源类型', 'name_en': 'res_type'},
        {'key': 'res_type_name', 'name_cn': '资源类型名称', 'name_en': 'res_type_name'},
        {'key': 'maintain_status', 'name_cn': '维护状态', 'name_en': 'maintain_status'},
        {'key': 'visible', 'name_cn': '可见性', 'name_en': 'visible', 'is_boolean': True},
    ]

    # 生成各个部分
    sections_html = ""
    sections_html += generate_field_section("基本告警信息 (Basic Alarm Information)", "📋", basic_fields)
    sections_html += generate_field_section("告警级别和状态 (Severity & Status)", "🚨", severity_fields)
    sections_html += generate_field_section("时间信息 (Time Information)", "⏰", time_fields)
    sections_html += generate_field_section("设备信息 (Device Information)", "🖥️", device_fields)

    # 渲染页面
    content = ALARM_DETAIL_CONTENT.format(alarm_sections=sections_html)

    return get_base_html(
        title=f"告警详情 - {alarm_dict.get('code_name', '未知告警')}",
        page_title="告警详情",
        content=content,
        extra_css=ALARM_DETAIL_CSS,
        extra_js=ALARM_DETAIL_JS
    )

@app.route('/api/stats')
def api_stats():
    """API - 获取统计信息"""
    conn = get_db_connection()
    
    # 总数统计
    total_count = conn.execute("SELECT COUNT(*) FROM alarms").fetchone()[0]
    
    # 级别统计
    severity_stats = conn.execute("""
        SELECT perceived_severity_name, COUNT(*) as count
        FROM alarms 
        WHERE perceived_severity_name IS NOT NULL
        GROUP BY perceived_severity_name 
        ORDER BY count DESC
    """).fetchall()
    
    # 类型统计
    type_stats = conn.execute("""
        SELECT alarm_type_name, COUNT(*) as count
        FROM alarms 
        WHERE alarm_type_name IS NOT NULL
        GROUP BY alarm_type_name 
        ORDER BY count DESC
        LIMIT 10
    """).fetchall()
    
    # 状态统计
    ack_stats = conn.execute("""
        SELECT ack_state_name, COUNT(*) as count
        FROM alarms 
        WHERE ack_state_name IS NOT NULL
        GROUP BY ack_state_name 
        ORDER BY count DESC
    """).fetchall()
    
    conn.close()
    
    return jsonify({
        'total_count': total_count,
        'severity_stats': [{'name': row[0], 'count': row[1]} for row in severity_stats],
        'type_stats': [{'name': row[0], 'count': row[1]} for row in type_stats],
        'ack_stats': [{'name': row[0], 'count': row[1]} for row in ack_stats]
    })

@app.route('/stats')
def stats():
    """统计页面"""
    html = BASE_HTML_TEMPLATE.format(
        title="统计信息 - 中兴网管告警查看器",
        page_title="统计信息",
        extra_css=STATS_CSS,
        content=STATS_CONTENT,
        extra_js=STATS_JS
    )
    return html

if __name__ == '__main__':
    print("启动中兴告警查看器...")
    print(f"数据库文件: {DB_FILE}")
    print("访问地址: http://localhost:8080")
    app.run(debug=False, host='0.0.0.0', port=8080)
