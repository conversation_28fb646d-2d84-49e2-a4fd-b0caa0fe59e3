#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import json
from datetime import datetime

def create_database_schema(db_path="zte_alarms.db"):
    """创建数据库表结构"""
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建主告警表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS alarms (
        -- 主键和基本标识
        id INTEGER PRIMARY KEY,
        alarm_key TEXT UNIQUE NOT NULL,
        sequence INTEGER,
        
        -- 告警基本信息
        alarm_code INTEGER,
        code_name TEXT,
        alarm_title TEXT,
        alarm_type INTEGER,
        alarm_type_name TEXT,
        reason_code INTEGER,
        reason_name TEXT,
        additional_text TEXT,
        
        -- 告警级别和状态
        perceived_severity INTEGER,
        perceived_severity_name TEXT,
        ack_state INTEGER,
        ack_state_name TEXT,
        ack_user_id TEXT,
        ack_time INTEGER,
        clear_state INTEGER,
        clear_state_name TEXT,
        
        -- 时间信息
        alarm_raised_time INTEGER,
        alarm_changed_time INTEGER,
        server_time INTEGER,
        offset_alarm_raised_time INTEGER,
        timezone_id TEXT,
        timezone_offset INTEGER,
        
        -- 设备信息
        me TEXT,
        me_name TEXT,
        ne_ip TEXT,
        nbi_id TEXT,
        moc TEXT,
        moc_name TEXT,
        
        -- 位置信息
        position TEXT,
        position_name TEXT,
        
        -- 资源信息
        res_type TEXT,
        res_type_name TEXT,
        
        -- 维护状态
        maintain_status TEXT,
        visible INTEGER,
        
        -- 其他状态
        relation_flag INTEGER,
        intermittence_count INTEGER,
        intermittence_duplicated_key TEXT,
        naf_filtered INTEGER,
        admc INTEGER,
        dstsaving INTEGER,
        
        -- 评论信息
        comment_text TEXT,
        comment_user_id TEXT,
        comment_time INTEGER,
        comment_system_id TEXT,
        ack_system_id TEXT,
        ack_info TEXT,
        
        -- 关联信息
        related_rules TEXT,
        link TEXT,
        link_name TEXT,
        alarm_source TEXT,
        aid TEXT,
        
        -- 复杂字段（JSON格式存储）
        ran_sdr_fm_native_param TEXT,
        ran_fm_alarm_object_name TEXT,
        ran_fm_alarm_service_id TEXT,
        ran_fm_alarm_site_name TEXT,
        ran_fm_ne_virtualization TEXT,
        product_res_type TEXT,
        ran_fm_alarm_dn TEXT,
        ran_fm_alarm_object_type TEXT,
        ran_fm_alarm_location TEXT,
        ran_fm_alarm_object TEXT,
        ran_fm_alarm_board_type TEXT,
        ran_fm_alarm_object_id TEXT,
        alarm_title_obj TEXT,
        maintain_status_obj TEXT,
        
        -- 数组字段（JSON格式存储）
        ne_plmns TEXT,
        operations TEXT,
        
        -- 创建和更新时间
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # 创建索引以提高查询性能
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_alarm_raised_time ON alarms(alarm_raised_time)",
        "CREATE INDEX IF NOT EXISTS idx_perceived_severity ON alarms(perceived_severity)",
        "CREATE INDEX IF NOT EXISTS idx_ack_state ON alarms(ack_state)",
        "CREATE INDEX IF NOT EXISTS idx_me_name ON alarms(me_name)",
        "CREATE INDEX IF NOT EXISTS idx_alarm_type ON alarms(alarm_type)",
        "CREATE INDEX IF NOT EXISTS idx_ne_ip ON alarms(ne_ip)",
        "CREATE INDEX IF NOT EXISTS idx_code_name ON alarms(code_name)",
        "CREATE INDEX IF NOT EXISTS idx_alarm_key ON alarms(alarm_key)"
    ]
    
    for index_sql in indexes:
        cursor.execute(index_sql)
    
    conn.commit()
    conn.close()
    
    print(f"数据库表结构创建完成: {db_path}")

def extract_nested_field_value(nested_obj):
    """提取嵌套字段的值"""
    if isinstance(nested_obj, dict):
        return nested_obj.get('value', '') or nested_obj.get('displayname', '')
    return str(nested_obj) if nested_obj is not None else ''

def extract_nested_field_json(nested_obj):
    """将嵌套字段转换为JSON字符串存储"""
    if isinstance(nested_obj, (dict, list)):
        return json.dumps(nested_obj, ensure_ascii=False)
    return str(nested_obj) if nested_obj is not None else ''

def insert_alarm_data(alarm_data, db_path="zte_alarms.db"):
    """将告警数据插入数据库"""
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 准备插入数据
    insert_data = {
        'id': alarm_data.get('id'),
        'alarm_key': alarm_data.get('alarmkey', ''),
        'sequence': alarm_data.get('sequence'),
        
        # 告警基本信息
        'alarm_code': alarm_data.get('alarmcode'),
        'code_name': alarm_data.get('codename', ''),
        'alarm_title': extract_nested_field_value(alarm_data.get('alarmtitle', {})),
        'alarm_type': alarm_data.get('alarmtype'),
        'alarm_type_name': alarm_data.get('alarmtypename', ''),
        'reason_code': alarm_data.get('reasoncode'),
        'reason_name': alarm_data.get('reasonname', ''),
        'additional_text': alarm_data.get('additionaltext', ''),
        
        # 告警级别和状态
        'perceived_severity': alarm_data.get('perceivedseverity'),
        'perceived_severity_name': alarm_data.get('perceivedseverityname', ''),
        'ack_state': alarm_data.get('ackstate'),
        'ack_state_name': alarm_data.get('ackstatename', ''),
        'ack_user_id': alarm_data.get('ackuserid', ''),
        'ack_time': alarm_data.get('acktime'),
        'clear_state': alarm_data.get('clearstate'),
        'clear_state_name': alarm_data.get('clearstatename', ''),
        
        # 时间信息
        'alarm_raised_time': alarm_data.get('alarmraisedtime'),
        'alarm_changed_time': alarm_data.get('alarmchangedtime'),
        'server_time': alarm_data.get('servertime'),
        'offset_alarm_raised_time': alarm_data.get('offsetalarmraisedtime'),
        'timezone_id': alarm_data.get('timezoneid', ''),
        'timezone_offset': alarm_data.get('timezoneoffset'),
        
        # 设备信息
        'me': alarm_data.get('me', ''),
        'me_name': alarm_data.get('mename', ''),
        'ne_ip': alarm_data.get('neip', ''),
        'nbi_id': alarm_data.get('nbiid', ''),
        'moc': alarm_data.get('moc', ''),
        'moc_name': alarm_data.get('mocname', ''),
        
        # 位置信息
        'position': alarm_data.get('position', ''),
        'position_name': alarm_data.get('positionname', ''),
        
        # 资源信息
        'res_type': alarm_data.get('restype', ''),
        'res_type_name': alarm_data.get('restypename', ''),
        
        # 维护状态
        'maintain_status': extract_nested_field_value(alarm_data.get('maintainstatus', {})),
        'visible': alarm_data.get('visible'),
        
        # 其他状态
        'relation_flag': alarm_data.get('relationflag'),
        'intermittence_count': alarm_data.get('intermittencecount'),
        'intermittence_duplicated_key': alarm_data.get('intermittenceduplicatedkey', ''),
        'naf_filtered': 1 if alarm_data.get('naffiltered') else 0,
        'admc': 1 if alarm_data.get('admc') else 0,
        'dstsaving': alarm_data.get('dstsaving'),
        
        # 评论信息
        'comment_text': alarm_data.get('commenttext', ''),
        'comment_user_id': alarm_data.get('commentuserid', ''),
        'comment_time': alarm_data.get('commenttime'),
        'comment_system_id': alarm_data.get('commentsystemid', ''),
        'ack_system_id': alarm_data.get('acksystemid', ''),
        'ack_info': alarm_data.get('ackinfo', ''),
        
        # 关联信息
        'related_rules': alarm_data.get('relatedrules', ''),
        'link': alarm_data.get('link', ''),
        'link_name': alarm_data.get('linkname', ''),
        'alarm_source': alarm_data.get('alarmsource', ''),
        'aid': alarm_data.get('aid', ''),
        
        # 复杂字段（JSON格式存储）
        'ran_sdr_fm_native_param': extract_nested_field_json(alarm_data.get('ran_sdr_fm_native_param')),
        'ran_fm_alarm_object_name': extract_nested_field_json(alarm_data.get('ran_fm_alarm_object_name')),
        'ran_fm_alarm_service_id': extract_nested_field_json(alarm_data.get('ran_fm_alarm_service_id')),
        'ran_fm_alarm_site_name': extract_nested_field_json(alarm_data.get('ran_fm_alarm_site_name')),
        'ran_fm_ne_virtualization': extract_nested_field_json(alarm_data.get('ran_fm_ne_virtualization')),
        'product_res_type': extract_nested_field_json(alarm_data.get('productRestype')),
        'ran_fm_alarm_dn': extract_nested_field_json(alarm_data.get('ran_fm_alarm_dn')),
        'ran_fm_alarm_object_type': extract_nested_field_json(alarm_data.get('ran_fm_alarm_object_type')),
        'ran_fm_alarm_location': extract_nested_field_json(alarm_data.get('ran_fm_alarm_location')),
        'ran_fm_alarm_object': extract_nested_field_json(alarm_data.get('ran_fm_alarm_object')),
        'ran_fm_alarm_board_type': extract_nested_field_json(alarm_data.get('ran_fm_alarm_board_type')),
        'ran_fm_alarm_object_id': extract_nested_field_json(alarm_data.get('ran_fm_alarm_object_id')),
        'alarm_title_obj': extract_nested_field_json(alarm_data.get('alarmtitle')),
        'maintain_status_obj': extract_nested_field_json(alarm_data.get('maintainstatus')),
        
        # 数组字段（JSON格式存储）
        'ne_plmns': extract_nested_field_json(alarm_data.get('neplmns')),
        'operations': extract_nested_field_json(alarm_data.get('operations'))
    }
    
    # 构建插入SQL
    columns = list(insert_data.keys())
    placeholders = ['?' for _ in columns]
    values = [insert_data[col] for col in columns]
    
    sql = f'''
    INSERT OR REPLACE INTO alarms ({', '.join(columns)})
    VALUES ({', '.join(placeholders)})
    '''
    
    cursor.execute(sql, values)
    conn.commit()
    conn.close()

if __name__ == "__main__":
    create_database_schema()
    print("数据库表结构创建完成！")
