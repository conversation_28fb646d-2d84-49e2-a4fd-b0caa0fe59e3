#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

try:
    response = requests.get("http://localhost:5000/")
    print(f"主页状态码: {response.status_code}")
    print(f"主页内容长度: {len(response.text)}")
    
    response = requests.get("http://localhost:5000/api/stats")
    print(f"API状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"API响应: {response.text[:200]}...")
    else:
        print(f"API错误: {response.text}")
        
except Exception as e:
    print(f"错误: {e}")
