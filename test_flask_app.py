#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, jsonify
import sqlite3

app = Flask(__name__)

@app.route('/')
def index():
    return "Hello World!"

@app.route('/api/stats')
def api_stats():
    try:
        conn = sqlite3.connect('zte_alarms.db')
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM alarms")
        count = cursor.fetchone()[0]
        conn.close()
        return jsonify({"total_count": count, "status": "ok"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/test')
def test():
    return jsonify({"message": "test route works"})

if __name__ == '__main__':
    print("启动测试Flask应用...")
    app.run(debug=True, host='0.0.0.0', port=5001)
