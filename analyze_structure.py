#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from collections import defaultdict

# 读取数据
with open('zte_active_alarms_raw_20250727_142332.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print("=== 数据结构分析 ===")
print(f"总记录数: {len(data)}")

# 分析字段类型
field_types = defaultdict(set)
nested_fields = []
simple_fields = []

sample_alarm = data[0]
for field_name, field_value in sample_alarm.items():
    if isinstance(field_value, dict):
        nested_fields.append(field_name)
        # 分析嵌套字段的结构
        for sub_key in field_value.keys():
            field_types[f"{field_name}.{sub_key}"].add(type(field_value[sub_key]).__name__)
    elif isinstance(field_value, list):
        field_types[field_name].add(f"list[{len(field_value)}]")
        if field_value:
            field_types[field_name].add(f"list_item_type: {type(field_value[0]).__name__}")
    else:
        simple_fields.append(field_name)
        field_types[field_name].add(type(field_value).__name__)

print(f"\n简单字段数量: {len(simple_fields)}")
print(f"嵌套字段数量: {len(nested_fields)}")

print("\n=== 嵌套字段分析 ===")
for field in nested_fields:
    print(f"\n{field}:")
    sample_value = sample_alarm[field]
    for key, value in sample_value.items():
        print(f"  {key}: {type(value).__name__} = {repr(value)[:100]}")

print("\n=== 简单字段类型分析 ===")
for field in simple_fields[:20]:  # 只显示前20个
    types = list(field_types[field])
    sample_value = sample_alarm[field]
    print(f"{field}: {types[0]} = {repr(sample_value)[:50]}")

print("\n=== 数组字段分析 ===")
for field_name, field_value in sample_alarm.items():
    if isinstance(field_value, list):
        print(f"\n{field_name}: {len(field_value)} 个元素")
        if field_value:
            print(f"  示例元素: {field_value[0]}")
