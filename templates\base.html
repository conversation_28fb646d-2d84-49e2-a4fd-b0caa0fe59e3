<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}中兴网管告警查看器{% endblock %}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            width: 100%;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            text-align: center;
            font-size: 2.2em;
            font-weight: 300;
        }
        
        .nav {
            text-align: center;
            margin-top: 15px;
        }
        
        .nav a {
            color: white;
            text-decoration: none;
            margin: 0 20px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav a:hover {
            background-color: rgba(255,255,255,0.2);
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        
        .table-container {
            width: 100%;
            overflow-x: auto;
            margin-top: 10px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1200px;
        }

        .table th,
        .table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: relative;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            resize: horizontal;
            user-select: none;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* 列宽调整手柄 */
        .table th {
            position: relative;
        }

        .table th::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            cursor: col-resize;
            background: transparent;
        }

        .table th:hover::after {
            background: #007bff;
        }

        /* 具体列宽设置 */
        .col-title { width: 200px; }
        .col-severity { width: 80px; }
        .col-status { width: 80px; }
        .col-device { width: 250px; }
        .col-ip { width: 120px; }
        .col-time { width: 150px; }
        .col-type { width: 120px; }
        .col-action { width: 80px; }
        .col-description { width: 300px; }
        
        .severity-严重 { color: #dc3545; font-weight: bold; }
        .severity-主要 { color: #fd7e14; font-weight: bold; }
        .severity-次要 { color: #ffc107; font-weight: bold; }
        .severity-警告 { color: #28a745; }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        
        .pagination a {
            padding: 8px 12px;
            text-decoration: none;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            color: #007bff;
        }
        
        .pagination a:hover {
            background-color: #e9ecef;
        }
        
        .pagination .current {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .search-form {
            display: flex;
            gap: 10px;
            align-items: end;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .search-form .form-group {
            margin-bottom: 0;
            min-width: 150px;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .text-muted {
            color: #6c757d;
        }
        
        .text-small {
            font-size: 0.875em;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .search-form {
                flex-direction: column;
            }

            .search-form .form-group {
                width: 100%;
            }

            .table {
                font-size: 11px;
                min-width: 800px;
            }

            .table th,
            .table td {
                padding: 6px 8px;
            }

            .col-title { width: 150px; }
            .col-device { width: 200px; }
            .col-description { width: 200px; }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>{% block page_title %}中兴网管告警查看器{% endblock %}</h1>
            <div class="nav">
                <a href="/">告警列表</a>
                <a href="/stats">统计信息</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        {% block content %}{% endblock %}
    </div>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
