{% extends "base.html" %}

{% block title %}告警详情 - {{ alarm.code_name or '未知告警' }}{% endblock %}
{% block page_title %}告警详情{% endblock %}

{% block extra_css %}
<style>
    .field-group {
        margin-bottom: 30px;
    }

    .field-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }

    .field-table th,
    .field-table td {
        padding: 8px 12px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
        vertical-align: top;
    }

    .field-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        width: 200px;
    }

    .field-name {
        font-weight: 500;
        color: #495057;
    }

    .field-name-en {
        font-size: 0.85em;
        color: #6c757d;
        font-weight: normal;
    }

    .field-value {
        word-break: break-all;
        max-width: 600px;
    }

    .json-container {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 10px;
        margin: 5px 0;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        max-height: 200px;
        overflow-y: auto;
    }

    .json-key {
        color: #0066cc;
        font-weight: bold;
    }

    .json-value {
        color: #009900;
    }

    .null-value {
        color: #999;
        font-style: italic;
    }

    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 10px 15px;
        border-radius: 6px;
        margin-bottom: 15px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div style="margin-bottom: 20px;">
    <a href="/" class="btn btn-secondary">&laquo; 返回列表</a>
    <button onclick="toggleAllSections()" class="btn btn-secondary">展开/收起所有</button>
    <button onclick="exportAlarmData()" class="btn">导出详情</button>
</div>

<!-- 基本告警信息 -->
<div class="card field-group">
    <div class="section-header">
        📋 基本告警信息 (Basic Alarm Information)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">记录ID</span><br><span class="field-name-en">id</span></th>
                <td class="field-value">{{ alarm.id or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">告警键</span><br><span class="field-name-en">alarm_key</span></th>
                <td class="field-value">{{ alarm.alarm_key or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">序列号</span><br><span class="field-name-en">sequence</span></th>
                <td class="field-value">{{ alarm.sequence or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">告警代码</span><br><span class="field-name-en">alarm_code</span></th>
                <td class="field-value">{{ alarm.alarm_code or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">告警标题</span><br><span class="field-name-en">code_name</span></th>
                <td class="field-value"><strong>{{ alarm.code_name or '-' }}</strong></td>
            </tr>
            <tr>
                <th><span class="field-name">告警标题对象</span><br><span class="field-name-en">alarm_title</span></th>
                <td class="field-value">{{ alarm.alarm_title or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">告警类型</span><br><span class="field-name-en">alarm_type</span></th>
                <td class="field-value">{{ alarm.alarm_type or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">告警类型名称</span><br><span class="field-name-en">alarm_type_name</span></th>
                <td class="field-value">{{ alarm.alarm_type_name or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">原因代码</span><br><span class="field-name-en">reason_code</span></th>
                <td class="field-value">{{ alarm.reason_code or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">原因描述</span><br><span class="field-name-en">reason_name</span></th>
                <td class="field-value">{{ alarm.reason_name or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">附加信息</span><br><span class="field-name-en">additional_text</span></th>
                <td class="field-value">{{ alarm.additional_text or '-' }}</td>
            </tr>
        </table>
    </div>
</div>

<!-- 告警级别和状态 -->
<div class="card field-group">
    <div class="section-header">
        🚨 告警级别和状态 (Severity & Status)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">感知严重性</span><br><span class="field-name-en">perceived_severity</span></th>
                <td class="field-value">{{ alarm.perceived_severity or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">告警级别</span><br><span class="field-name-en">perceived_severity_name</span></th>
                <td class="field-value">
                    <span class="severity-{{ alarm.perceived_severity_name or '' }}">
                        {{ alarm.perceived_severity_name or '-' }}
                    </span>
                </td>
            </tr>
            <tr>
                <th><span class="field-name">确认状态</span><br><span class="field-name-en">ack_state</span></th>
                <td class="field-value">{{ alarm.ack_state or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">确认状态名称</span><br><span class="field-name-en">ack_state_name</span></th>
                <td class="field-value">{{ alarm.ack_state_name or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">确认用户ID</span><br><span class="field-name-en">ack_user_id</span></th>
                <td class="field-value">{{ alarm.ack_user_id or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">确认时间</span><br><span class="field-name-en">ack_time</span></th>
                <td class="field-value">{{ alarm.ack_time_formatted or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">清除状态</span><br><span class="field-name-en">clear_state</span></th>
                <td class="field-value">{{ alarm.clear_state or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">清除状态名称</span><br><span class="field-name-en">clear_state_name</span></th>
                <td class="field-value">{{ alarm.clear_state_name or '-' }}</td>
            </tr>
        </table>
    </div>
</div>

<!-- 时间信息 -->
<div class="card field-group">
    <div class="section-header">
        ⏰ 时间信息 (Time Information)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">告警发生时间</span><br><span class="field-name-en">alarm_raised_time</span></th>
                <td class="field-value">
                    {{ alarm.alarm_raised_time_formatted or '-' }}
                    {% if alarm.alarm_raised_time %}<br><small class="text-muted">原始值: {{ alarm.alarm_raised_time }}</small>{% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">告警变更时间</span><br><span class="field-name-en">alarm_changed_time</span></th>
                <td class="field-value">
                    {{ alarm.alarm_changed_time_formatted or '-' }}
                    {% if alarm.alarm_changed_time %}<br><small class="text-muted">原始值: {{ alarm.alarm_changed_time }}</small>{% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">服务器时间</span><br><span class="field-name-en">server_time</span></th>
                <td class="field-value">
                    {{ alarm.server_time_formatted or '-' }}
                    {% if alarm.server_time %}<br><small class="text-muted">原始值: {{ alarm.server_time }}</small>{% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">偏移告警时间</span><br><span class="field-name-en">offset_alarm_raised_time</span></th>
                <td class="field-value">
                    {{ alarm.offset_alarm_raised_time_formatted or '-' }}
                    {% if alarm.offset_alarm_raised_time %}<br><small class="text-muted">原始值: {{ alarm.offset_alarm_raised_time }}</small>{% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">时区ID</span><br><span class="field-name-en">timezone_id</span></th>
                <td class="field-value">{{ alarm.timezone_id or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">时区偏移</span><br><span class="field-name-en">timezone_offset</span></th>
                <td class="field-value">{{ alarm.timezone_offset or '-' }}</td>
            </tr>
        </table>
    </div>
</div>

<!-- 设备信息 -->
<div class="card field-group">
    <div class="section-header">
        🖥️ 设备信息 (Device Information)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">管理元素</span><br><span class="field-name-en">me</span></th>
                <td class="field-value">{{ alarm.me or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">设备名称</span><br><span class="field-name-en">me_name</span></th>
                <td class="field-value">{{ alarm.me_name or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">网元IP</span><br><span class="field-name-en">ne_ip</span></th>
                <td class="field-value">{{ alarm.ne_ip or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">NBI ID</span><br><span class="field-name-en">nbi_id</span></th>
                <td class="field-value">{{ alarm.nbi_id or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">MOC</span><br><span class="field-name-en">moc</span></th>
                <td class="field-value">{{ alarm.moc or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">MOC名称</span><br><span class="field-name-en">moc_name</span></th>
                <td class="field-value">{{ alarm.moc_name or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">位置</span><br><span class="field-name-en">position</span></th>
                <td class="field-value">{{ alarm.position or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">位置名称</span><br><span class="field-name-en">position_name</span></th>
                <td class="field-value">{{ alarm.position_name or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">资源类型</span><br><span class="field-name-en">res_type</span></th>
                <td class="field-value">{{ alarm.res_type or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">资源类型名称</span><br><span class="field-name-en">res_type_name</span></th>
                <td class="field-value">{{ alarm.res_type_name or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">维护状态</span><br><span class="field-name-en">maintain_status</span></th>
                <td class="field-value">{{ alarm.maintain_status or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">可见性</span><br><span class="field-name-en">visible</span></th>
                <td class="field-value">{{ '是' if alarm.visible else '否' if alarm.visible is not none else '-' }}</td>
            </tr>
        </table>
    </div>
</div>

<!-- 系统状态和标志 -->
<div class="card field-group">
    <div class="section-header">
        ⚙️ 系统状态和标志 (System Status & Flags)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">关联标志</span><br><span class="field-name-en">relation_flag</span></th>
                <td class="field-value">{{ alarm.relation_flag or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">间歇次数</span><br><span class="field-name-en">intermittence_count</span></th>
                <td class="field-value">{{ alarm.intermittence_count or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">间歇重复键</span><br><span class="field-name-en">intermittence_duplicated_key</span></th>
                <td class="field-value">{{ alarm.intermittence_duplicated_key or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">NAF过滤</span><br><span class="field-name-en">naf_filtered</span></th>
                <td class="field-value">{{ '是' if alarm.naf_filtered else '否' if alarm.naf_filtered is not none else '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">ADMC</span><br><span class="field-name-en">admc</span></th>
                <td class="field-value">{{ '是' if alarm.admc else '否' if alarm.admc is not none else '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">DST保存</span><br><span class="field-name-en">dstsaving</span></th>
                <td class="field-value">{{ alarm.dstsaving or '-' }}</td>
            </tr>
        </table>
    </div>
</div>

<!-- 处理信息 -->
<div class="card field-group">
    <div class="section-header">
        💬 处理信息 (Processing Information)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">评论文本</span><br><span class="field-name-en">comment_text</span></th>
                <td class="field-value">{{ alarm.comment_text or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">评论用户ID</span><br><span class="field-name-en">comment_user_id</span></th>
                <td class="field-value">{{ alarm.comment_user_id or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">评论时间</span><br><span class="field-name-en">comment_time</span></th>
                <td class="field-value">
                    {{ alarm.comment_time_formatted or '-' }}
                    {% if alarm.comment_time %}<br><small class="text-muted">原始值: {{ alarm.comment_time }}</small>{% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">评论系统ID</span><br><span class="field-name-en">comment_system_id</span></th>
                <td class="field-value">{{ alarm.comment_system_id or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">确认系统ID</span><br><span class="field-name-en">ack_system_id</span></th>
                <td class="field-value">{{ alarm.ack_system_id or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">确认信息</span><br><span class="field-name-en">ack_info</span></th>
                <td class="field-value">{{ alarm.ack_info or '-' }}</td>
            </tr>
        </table>
    </div>
</div>

<!-- 关联信息 -->
<div class="card field-group">
    <div class="section-header">
        🔗 关联信息 (Related Information)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">相关规则</span><br><span class="field-name-en">related_rules</span></th>
                <td class="field-value">{{ alarm.related_rules or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">链接</span><br><span class="field-name-en">link</span></th>
                <td class="field-value">{{ alarm.link or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">链接名称</span><br><span class="field-name-en">link_name</span></th>
                <td class="field-value">{{ alarm.link_name or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">告警源</span><br><span class="field-name-en">alarm_source</span></th>
                <td class="field-value">{{ alarm.alarm_source or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">AID</span><br><span class="field-name-en">aid</span></th>
                <td class="field-value">{{ alarm.aid or '-' }}</td>
            </tr>
        </table>
    </div>
</div>

<!-- RAN扩展字段 -->
<div class="card field-group">
    <div class="section-header">
        📡 RAN扩展字段 (RAN Extended Fields)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">SDR FM原生参数</span><br><span class="field-name-en">ran_sdr_fm_native_param</span></th>
                <td class="field-value">
                    {% if alarm.ran_sdr_fm_native_param_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_sdr_fm_native_param_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_sdr_fm_native_param or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">告警对象名称</span><br><span class="field-name-en">ran_fm_alarm_object_name</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_alarm_object_name_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_alarm_object_name_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_alarm_object_name or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">业务ID</span><br><span class="field-name-en">ran_fm_alarm_service_id</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_alarm_service_id_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_alarm_service_id_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_alarm_service_id or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">站点名称</span><br><span class="field-name-en">ran_fm_alarm_site_name</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_alarm_site_name_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_alarm_site_name_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_alarm_site_name or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">网元虚拟化</span><br><span class="field-name-en">ran_fm_ne_virtualization</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_ne_virtualization_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_ne_virtualization_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_ne_virtualization or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">产品资源类型</span><br><span class="field-name-en">product_res_type</span></th>
                <td class="field-value">
                    {% if alarm.product_res_type_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.product_res_type_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.product_res_type or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">DN路径</span><br><span class="field-name-en">ran_fm_alarm_dn</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_alarm_dn_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_alarm_dn_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_alarm_dn or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">告警对象类型</span><br><span class="field-name-en">ran_fm_alarm_object_type</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_alarm_object_type_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_alarm_object_type_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_alarm_object_type or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">定位信息</span><br><span class="field-name-en">ran_fm_alarm_location</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_alarm_location_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_alarm_location_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_alarm_location or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">告警对象</span><br><span class="field-name-en">ran_fm_alarm_object</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_alarm_object_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_alarm_object_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_alarm_object or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">单板类型</span><br><span class="field-name-en">ran_fm_alarm_board_type</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_alarm_board_type_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_alarm_board_type_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_alarm_board_type or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">告警对象ID</span><br><span class="field-name-en">ran_fm_alarm_object_id</span></th>
                <td class="field-value">
                    {% if alarm.ran_fm_alarm_object_id_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.ran_fm_alarm_object_id_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ran_fm_alarm_object_id or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>
</div>

<!-- 对象字段 -->
<div class="card field-group">
    <div class="section-header">
        📦 对象字段 (Object Fields)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">告警标题对象</span><br><span class="field-name-en">alarm_title_obj</span></th>
                <td class="field-value">
                    {% if alarm.alarm_title_obj_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.alarm_title_obj_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.alarm_title_obj or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">维护状态对象</span><br><span class="field-name-en">maintain_status_obj</span></th>
                <td class="field-value">
                    {% if alarm.maintain_status_obj_parsed %}
                        <div class="json-container">
                            {% for key, value in alarm.maintain_status_obj_parsed.items() %}
                                <div><span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span></div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.maintain_status_obj or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>
</div>

<!-- 数组字段 -->
<div class="card field-group">
    <div class="section-header">
        📋 数组字段 (Array Fields)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">网元PLMN</span><br><span class="field-name-en">ne_plmns</span></th>
                <td class="field-value">
                    {% if alarm.ne_plmns_parsed %}
                        <div class="json-container">
                            {% for item in alarm.ne_plmns_parsed %}
                                <div style="margin-bottom: 5px; padding: 5px; background: #fff; border: 1px solid #ddd; border-radius: 3px;">
                                    {% if item is mapping %}
                                        {% for key, value in item.items() %}
                                            <span class="json-key">{{ key }}:</span> <span class="json-value">{{ value }}</span><br>
                                        {% endfor %}
                                    {% else %}
                                        <span class="json-value">{{ item }}</span>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.ne_plmns or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th><span class="field-name">可用操作</span><br><span class="field-name-en">operations</span></th>
                <td class="field-value">
                    {% if alarm.operations_parsed %}
                        <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                            {% for operation in alarm.operations_parsed %}
                                <span style="display: inline-block; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                    {{ operation }}
                                </span>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="null-value">{{ alarm.operations or '-' }}</span>
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>
</div>

<!-- 系统字段 -->
<div class="card field-group">
    <div class="section-header">
        🗄️ 系统字段 (System Fields)
    </div>
    <div class="card-body">
        <table class="field-table">
            <tr>
                <th><span class="field-name">创建时间</span><br><span class="field-name-en">created_at</span></th>
                <td class="field-value">{{ alarm.created_at or '-' }}</td>
            </tr>
            <tr>
                <th><span class="field-name">更新时间</span><br><span class="field-name-en">updated_at</span></th>
                <td class="field-value">{{ alarm.updated_at or '-' }}</td>
            </tr>
        </table>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// 展开/收起所有部分
function toggleAllSections() {
    const sections = document.querySelectorAll('.field-group');
    const firstSection = sections[0];
    const isCollapsed = firstSection.style.display === 'none';

    sections.forEach(section => {
        section.style.display = isCollapsed ? 'block' : 'none';
    });
}

// 导出告警详情数据
function exportAlarmData() {
    const alarmData = {};

    // 收集所有字段数据
    const tables = document.querySelectorAll('.field-table');
    tables.forEach(table => {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            const th = row.querySelector('th');
            const td = row.querySelector('td');
            if (th && td) {
                const fieldNameEn = th.querySelector('.field-name-en');
                const fieldName = th.querySelector('.field-name');
                if (fieldNameEn && fieldName) {
                    const key = fieldNameEn.textContent.trim();
                    const value = td.textContent.trim();
                    alarmData[key] = value;
                }
            }
        });
    });

    // 创建JSON文件并下载
    const jsonStr = JSON.stringify(alarmData, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `alarm_detail_${alarmData.id || 'unknown'}_${new Date().toISOString().slice(0,10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 为每个部分添加点击展开/收起功能
    const headers = document.querySelectorAll('.section-header');
    headers.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const cardBody = this.nextElementSibling;
            if (cardBody) {
                const isHidden = cardBody.style.display === 'none';
                cardBody.style.display = isHidden ? 'block' : 'none';

                // 添加展开/收起图标
                const icon = this.querySelector('.toggle-icon') || document.createElement('span');
                icon.className = 'toggle-icon';
                icon.style.float = 'right';
                icon.textContent = isHidden ? '▼' : '▲';
                if (!this.querySelector('.toggle-icon')) {
                    this.appendChild(icon);
                }
            }
        });

        // 初始添加图标
        const icon = document.createElement('span');
        icon.className = 'toggle-icon';
        icon.style.float = 'right';
        icon.textContent = '▼';
        header.appendChild(icon);
    });

    // 添加搜索功能
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = '搜索字段名或值...';
    searchInput.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 8px 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        z-index: 1000;
        background: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    `;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('.field-table tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    document.body.appendChild(searchInput);
});
</script>
{% endblock %}
